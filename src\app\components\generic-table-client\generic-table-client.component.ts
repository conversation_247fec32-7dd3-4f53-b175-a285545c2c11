import {
  Component,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  AfterViewInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
// import { MatPaginator, MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-generic-table-client',
  imports: [CommonModule, MatIconModule, MatMenuModule, MatButtonModule],
  templateUrl: './generic-table-client.component.html',
  styleUrl: './generic-table-client.component.css',
  standalone: true,
})
export class GenericTableClientComponent {
  @Input() headers: { header: string; colspan: number }[] = [];
  @Input() keys: string[] = [];
  @Input() colspans: string[] = [];
  @Input() data: any[] = [];
  @Input() actions: string[] = [];

  @Output() actionTriggered = new EventEmitter<{ action: string; row: any }>();

  ngAfterViewInit(): void {}

  // Keep the action handling methods
  triggerAction(action: string, row: any): void {
    this.actionTriggered.emit({ action, row });
  }

  logRow(row: any): void {
    console.log('Row details:', row);
  }

  getActionIcon(action: string): string {
    switch (action) {
      case 'view':
        return 'visibility';
      case 'edit':
        return 'edit';
      case 'delete':
        return 'delete';
      default:
        return '';
    }
  }

  getActionClass(action: string): string {
    switch (action) {
      case 'view':
        return 'action-view';
      case 'edit':
        return 'action-edit';
      case 'delete':
        return 'action-delete';
      default:
        return '';
    }
  }

  getActionLabel(action: string): string {
    switch (action) {
      case 'view':
        return 'Détails';
      case 'edit':
        return 'Modifier';
      case 'delete':
        return 'Supprimer';
      default:
        return action;
    }
  }

  getValue(row: any, key: string): any {
    if (key.split('.').length > 1) {
      const keys = key.split('.');
      return row[keys[0]] ? row[keys[0]][keys[1]] : null;
    }
    return row[key];
  }

  isStatusColumn(key: string): boolean {
    const statusKeys = [
      'status',
      'state',
      'statut',
      'état',
      'etat',
      'sites status',
    ];
    return statusKeys.some((statusKey) =>
      key.toLowerCase().includes(statusKey.toLowerCase())
    );
  }

  getStatusClass(key: string, value: any): string {
    if (!this.isStatusColumn(key) || !value) {
      return '';
    }

    const valueStr = value.toString().toLowerCase();

    // Check for active/inactive variations
    if (['actif', 'active', 'activé', 'true', '1'].includes(valueStr)) {
      return 'status-active';
    }

    if (
      ['inactif', 'inactive', 'désactivé', 'desactive', 'false', '0'].includes(
        valueStr
      )
    ) {
      return 'status-inactive';
    }

    // Add more status types if needed
    if (['maintenance', 'en maintenance'].includes(valueStr)) {
      return 'status-maintenance';
    }

    if (['retiré', 'retire'].includes(valueStr)) {
      return 'status-retire';
    }

    if (['erreur', 'error', 'échec', 'echec'].includes(valueStr)) {
      return 'status-error';
    }

    return '';
  }

  getCellClass(key: string, value: any): string {
    return this.isStatusColumn(key) ? 'status-cell' : '';
  }

  getSiteStatusClassByIndex(index: string): string {
    switch (index) {
      case 'SiteActif':
        return 'status-green';
      case 'SiteInactif':
        return 'status-red';
      case 'SiteEnMaintenance':
        return 'status-blue';
      case 'SiteEnInstallation':
        return 'status-installation';
      default:
        return '';
    }
  }
}
