<app-success></app-success>
<ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
<div class="organisation-management-container">
  <div class="header-section">
    <div class="page-title">
      <h1 class="title">
        <i class="material-icons title-icon">assignment</i> Liste des Affectations
      </h1>
    </div>
    <div class="actions">
      <button class="create-button" (click)="openTagAssignmentDialog()">
        <i class="material-icons action-icon">add</i> Nouvelle Affectation
      </button>
    </div>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Chargement des affectations...</p>
  </div>

  <div class="search-bar-container">
    <div class="search-bar">
      <input type="text" [(ngModel)]="searchTerm" placeholder="Rechercher par tag, cible ou type" class="search-input"
        (keyup.enter)="filterAssignments()"
        (input)="filterAssignments()" />
      <button class="clear-btn" (click)="clearSearch()" *ngIf="searchTerm">
        <mat-icon style="font-size: 22px;">close</mat-icon>
      </button>
    </div>
    <span class="search-button" (click)="filterAssignments()">
      <mat-icon>search</mat-icon>
    </span>
  </div>

  <!-- Assignments table -->
  <div class="table-section" *ngIf="!isLoading" style="width: 100%;">
    <app-generic-table-client
      [data]="assignments"
      [headers]="headers"
      [keys]="keys"
      [actions]="['delete']"
      (actionTriggered)="handleAction($event)">
    </app-generic-table-client>
    <div class="pagination-container">
      <mat-paginator
        [length]="totalCount"
        [pageSize]="pageSize"
        [pageIndex]="currentPage"
        [pageSizeOptions]="[5, 10, 25, 50]"
        (page)="onPageChange($event)"
        aria-label="Select page">
      </mat-paginator>
    </div>
  </div>
  <ngx-ui-loader></ngx-ui-loader>
</div>
