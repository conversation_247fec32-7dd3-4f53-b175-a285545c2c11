<!-- Tag List Component -->
<div class="local-management-container">
  <!-- Header Section -->
  <div class="header-section">
    <div class="page-title">
      <h1 class="title">
        <mat-icon class="title-icon">local_offer</mat-icon> Liste des Tags
      </h1>
    </div>
    <div class="actions">
      <button class="create-button" (click)="showCreateTagForm()">
        <mat-icon class="action-icon">add</mat-icon> Créer Tag
      </button>
    </div>
  </div>

  <!-- Search Section -->
  <div class="search-bar">
    <input
      type="text"
      [value]="searchTerm"
      (input)="searchTerm = $any($event.target).value"
      placeholder="Rechercher un tag"
      (keyup)="onSearchKeyup($event)"
    />
    <button class="search-button" (click)="filterTags()">
      <mat-icon>search</mat-icon>
    </button>
  </div>

  <div class="loading-spinner" *ngIf="isLoading">Chargement...</div>

  <!-- Tag Table -->
  <div class="table-view" *ngIf="!isLoading">
    <div class="subscription-table-container">
      <div class="table-color-bar"></div>
      <div class="table-wrapper">
        <table class="generic-table">
          <thead>
            <tr>
              <th *ngFor="let header of headers">{{ header }}</th>
              <th *ngIf="tableActions.length > 0">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngIf="transformedTags.length === 0">
              <td [colSpan]="headers.length + (tableActions.length > 0 ? 1 : 0)">
                Aucun tag trouvé
              </td>
            </tr>
            <tr *ngFor="let row of transformedTags; let i = index" class="table-row">
              <td *ngFor="let key of keys">
                {{ row[key] != null ? row[key] : '-' }}
              </td>
              <td *ngIf="tableActions.length > 0">
                <div class="actions-container">
                  <button
                    mat-icon-button
                    (click)="triggerAction('edit', row)"
                    class="action-button edit-button"
                    title="Modifier"
                  >
                    <mat-icon>edit</mat-icon>
                  </button>

                  <button
                    mat-icon-button
                    (click)="triggerAction('delete', row)"
                    class="action-button delete-button"
                    title="Supprimer"
                  >
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="pagination-container">
  <mat-paginator
  [length]="totalCount"
  [pageSize]="pageSize"
  [pageIndex]="currentPage"
  [pageSizeOptions]="[5, 10, 25, 50]"
  (page)="onPageChange($event)"
  aria-label="Select page">
</mat-paginator>
    </div>
  </div>

  <!-- Create/Edit Tag Popup -->
  <div class="popup-overlay" *ngIf="showTagForm" (click)="hideTagForm()">
    <div class="popup-form" (click)="$event.stopPropagation()">
      <div class="popup-header">
        <h3>
          <mat-icon>{{ tagForm.get('id')?.value ? 'edit' : 'add' }}</mat-icon>
          {{ tagForm.get('id')?.value ? 'Modifier Tag' : 'Créer Tag' }}
        </h3>
        <button class="close-btn" (click)="hideTagForm()">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <form [formGroup]="tagForm" (ngSubmit)="onSubmitTag()" class="site-form">
        <div class="validation-errors" *ngIf="tagForm.invalid && (tagForm.touched || tagForm.dirty)">
          <div class="validation-errors-title">
            <mat-icon>error_outline</mat-icon>
            Erreurs de validation
          </div>
          <ul class="validation-errors-list">
            <li *ngIf="tagForm.get('nom')?.invalid">
              <mat-icon>error</mat-icon>
              Le nom du tag est requis (minimum 2 caractères)
            </li>
          </ul>
        </div>

        <div class="form-grid">
          <div class="form-group full-width">
            <label for="tagName">Nom du Tag <span class="required">*</span></label>
            <input
              id="tagName"
              type="text"
              formControlName="nom"
              placeholder="Entrez le nom du tag"
              required
            />
          </div>
        </div>

        <div class="form-actions">
          <button type="button" class="btn-cancel" (click)="hideTagForm()">
            Annuler
          </button>
          <button type="submit" class="btn-submit" [disabled]="!tagForm.valid || isSubmitting">
            {{ isSubmitting ? 'Enregistrement...' : (tagForm.get('id')?.value ? 'Modifier' : 'Créer') }}
          </button>
        </div>
      </form>
    </div>
  </div>

  <ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
</div>
