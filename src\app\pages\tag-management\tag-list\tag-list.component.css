.local-management-container {
  width: 95%;
  margin: 30px auto;
  padding: 25px;
  background: linear-gradient(145deg, #ffffff, #f9f9f9);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

/* Header Section */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  width: 95%;
}

.title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.title-icon {
  font-size: 26px;
  color: var(--primary);
}

.create-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.create-button:hover {
  background: linear-gradient(45deg, #81c784, var(--primary)) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.action-icon {
  font-size: 18px;
}

/* Search Bar */
.search-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  align-items: center;
}

.search-bar input {
  flex: 1;
  max-width: 450px;
  padding: 12px 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #f8fafc;
  color: #4a5568;
}

.search-bar input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.search-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white;
  padding: 7px 25px;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.3s ease;
}

.search-button:hover {
  background: linear-gradient(45deg, #81c784, var(--primary)) !important;
}

/* Table View */
.table-view {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* Generic Table Styles */
.subscription-table-container {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  margin-top: 20px;
  width: 100%;
}

.table-color-bar {
  height: 0.5rem;
  background: linear-gradient(to right, var(--primary), rgba(52, 211, 153, 0.747));
}

.table-wrapper {
  overflow-x: auto;
}

.generic-table {
  width: 100%;
  border-collapse: collapse;
}

.generic-table th {
  padding: 1rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #111827;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-family: "Montserrat", sans-serif;
  text-align: center;
}

.generic-table td {
  padding: 1rem 1.5rem;
  font-size: 0.875rem;
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
  font-family: "Lato", sans-serif;
  vertical-align: middle;
  text-align: center;
}

.generic-table tr:last-child td {
  border-bottom: none;
}

.generic-table tr:hover {
  background-color: #f9fafb;
}

/* Actions container */
.actions-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

/* Action buttons */
.action-button {
  min-width: 36px;
  width: 36px;
  height: 36px;
  padding: 0;
  line-height: 1;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.action-button mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

/* Edit button styling */
.edit-button {
  background-color: rgba(255, 152, 0, 0.1);
}

.edit-button:hover {
  background-color: rgba(255, 152, 0, 0.2);
  transform: scale(1.1);
}

.edit-button mat-icon {
  color: #ff9800;
}

/* Delete button styling */
.delete-button {
  background-color: rgba(244, 67, 54, 0.1);
}

.delete-button:hover {
  background-color: rgba(244, 67, 54, 0.2);
  transform: scale(1.1);
}

.delete-button mat-icon {
  color: #f44336;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .generic-table td,
  .generic-table th {
    padding: 0.75rem 1rem;
  }
}

/* Loading Spinner */
.loading-spinner {
  text-align: center;
  padding: 40px;
  font-size: 16px;
  color: #666;
}

/* Popup Form Styles */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.popup-form {
  background: white;
  border-radius: 12px;
  padding: 30px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e2e8f0;
}

.popup-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  display: flex;
  align-items: center;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #718096;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f7fafc;
  color: #2d3748;
}

/* Form Styles */
.site-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #2d3748;
  font-size: 14px;
}

.required {
  color: #e53e3e;
}

.form-group input {
  padding: 12px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #f8fafc;
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
  background: white;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.btn-cancel {
  padding: 12px 24px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #4a5568;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.btn-submit {
  padding: 12px 24px;
  background: linear-gradient(45deg, var(--primary), #81c784);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-submit:hover:not(:disabled) {
  background: linear-gradient(45deg, #81c784, var(--primary));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.btn-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Validation Errors */
.validation-errors {
  background: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.validation-errors-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #c53030;
  margin-bottom: 10px;
}

.validation-errors-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.validation-errors-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #c53030;
  font-size: 14px;
  margin-bottom: 5px;
}

.validation-errors-list li:last-child {
  margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .popup-form {
    width: 95%;
    padding: 20px;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-cancel,
  .btn-submit {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 24px;
  }

  .search-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-bar input {
    max-width: none;
  }
}